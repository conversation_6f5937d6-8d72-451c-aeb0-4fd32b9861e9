<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  getDay,
  addMonths,
  subMonths,
  isSameDay,
  isToday,
  isEqual,
  parseISO,
} from 'date-fns';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';
import { useI18n } from '@/composables/useI18n.ts';

interface CalendarEvent {
  id: string | number;
  title: string;
  date: Date;
  category: number;
  [key: string]: any;
}

const props = defineProps<{
  events?: CalendarEvent[];
  initialMonth?: Date;
  daySelected?: Date;
}>();

const emit = defineEmits<{
  'day-click': [payload: { date: Date; events: CalendarEvent[] }];
  'event-click': [payload: { event: CalendarEvent; date: Date }];
  'month-change': [payload: { month: Date }];
  'more-click': [payload: { date: Date; events: CalendarEvent[]; moreCount: number }];
}>();

const { t } = useI18n();
const currentMonth = ref(props.initialMonth || new Date());
const daySelected = ref(props.daySelected || new Date());

watch(
  () => props.initialMonth,
  newMonth => {
    if (newMonth) {
      currentMonth.value = newMonth;
    }
  },
  { immediate: true },
);

const previousMonth = () => {
  currentMonth.value = subMonths(currentMonth.value, 1);
  emit('month-change', { month: currentMonth.value });
};

const nextMonth = () => {
  currentMonth.value = addMonths(currentMonth.value, 1);
  emit('month-change', { month: currentMonth.value });
};

const goToToday = () => {
  currentMonth.value = new Date();
  emit('month-change', { month: currentMonth.value });
};

const getEventsForDay = (day: Date) => {
  if (!props.events) return [];
  return props.events.filter(event => {
    const eventDate = event.date;
    return isSameDay(eventDate, day);
  });
};

const handleDayClick = (day: Date) => {
  const events = getEventsForDay(day);
  daySelected.value = day;
  emit('day-click', { date: day, events });
};

const handleEventClick = (event: CalendarEvent, day: Date, e: Event) => {
  e.stopPropagation();
  emit('event-click', { event, date: day });
};

const handleMoreClick = (day: Date, e: Event) => {
  e.stopPropagation();
  const events = getEventsForDay(day);
  const moreCount = events.length - MAX_EVENTS;
  emit('more-click', { date: day, events, moreCount });
};

const getDaysInMonth = computed(() => {
  const start = startOfMonth(currentMonth.value);
  const end = endOfMonth(currentMonth.value);
  return eachDayOfInterval({ start, end });
});

const getFirstDayOfMonth = computed(() => {
  const firstDay = startOfMonth(currentMonth.value);

  return (getDay(firstDay) + 6) % 7;
});

const needsSixRows = computed(() => {
  const daysInMonth = getDaysInMonth.value.length;
  const firstDayOffset = getFirstDayOfMonth.value;

  return daysInMonth + firstDayOffset > 35;
});

const getEmptyCellsAfterMonth = computed(() => {
  const daysInMonth = getDaysInMonth.value.length;
  const firstDayOffset = getFirstDayOfMonth.value;
  const totalCells = needsSixRows.value ? 42 : 35;

  return totalCells - (daysInMonth + firstDayOffset);
});

const formattedMonth = computed(() => {
  return format(currentMonth.value, 'yyyy MM');
});

const MAX_EVENTS = 2;

const getLimitedEventsForDay = (day: Date) => {
  const events = getEventsForDay(day);

  return {
    events: events.slice(0, MAX_EVENTS),
    hasMore: true,
    moreCount: events.length - MAX_EVENTS,
  };
};

const daysOfWeek = [
  t('common.calendar.mon'),
  t('common.calendar.tue'),
  t('common.calendar.wed'),
  t('common.calendar.thu'),
  t('common.calendar.fri'),
  t('common.calendar.sat'),
  t('common.calendar.sun'),
];
</script>

<template>
  <div class="overflow-hidden p-4">
    <div class="p-2 flex flex-col gap-2 bg-blue-50">
      <div class="flex items-center justify-between">
        <div class="flex flex-1">
          <button @click="previousMonth" class="p-2 rounded hover:bg-blue-100">
            <ChevronLeftIcon class="w-5 h-5 text-blue-600" />
          </button>
          <button
            @click="goToToday"
            class="px-3 py-1 mx-1 text-sm font-medium text-blue-700 bg-blue-100 rounded cursor-pointer hover:bg-blue-200 transition-colors"
          >
            {{ t('common.calendar.currentMonth') }}
          </button>
          <button @click="nextMonth" class="p-2 rounded hover:bg-blue-100">
            <ChevronRightIcon class="w-5 h-5 text-blue-600" />
          </button>
        </div>
        <h2 class="text-xl font-bold text-blue-800 capitalize flex-1 text-center">
          {{ formattedMonth }}
        </h2>
        <div class="flex-1"></div>
      </div>
    </div>

    <div class="py-4">
      <div class="grid grid-cols-7 gap-1 mb-2">
        <div
          v-for="day in daysOfWeek"
          :key="day"
          class="text-center text-sm font-medium border py-1 border-gray-100 bg-gray-100"
        >
          {{ day }}
        </div>
      </div>

      <div class="grid grid-cols-7 gap-1">
        <div
          v-for="_ in getFirstDayOfMonth"
          :key="'empty-before-' + _"
          class="max-sm:h-20 sm:h-24 border border-gray-100"
        />

        <div
          v-for="day in getDaysInMonth"
          :key="day.toISOString()"
          class="max-sm:h-20 sm:h-24 border border-gray-100 p-1 relative cursor-pointer hover:bg-gray-50 transition-colors overflow-hidden"
          :class="{
            'bg-blue-50': isToday(day),
            'bg-blue-100': isEqual(daySelected, day),
          }"
          @click="handleDayClick(day)"
        >
          <div class="text-sm font-medium text-gray-700">
            {{ format(day, 'd') }}
          </div>

          <div class="mt-1 space-y-1 overflow-y-auto max-h-16 max-sm:max-h-[calc(100%-24px)]">
            <template v-if="getLimitedEventsForDay(day).events.length > 0">
              <div
                class="text-xs px-1 py-0.5 rounded truncate cursor-pointer bg-blue-100 text-blue-800"
                v-for="event in getLimitedEventsForDay(day).events"
                :key="event.id"
                @click="e => handleEventClick(event, day, e)"
              >
                {{ event.title }}
              </div>

              <div
                v-if="getLimitedEventsForDay(day).hasMore"
                class="text-xs px-1 py-0.5 text-blue-600 font-medium text-center bg-blue-50 rounded cursor-pointer hover:bg-blue-100 transition-colors"
                @click="handleMoreClick(day, $event)"
              >
                +{{ getLimitedEventsForDay(day).moreCount }} more
              </div>
            </template>
          </div>
        </div>
        <div
          v-for="_ in getEmptyCellsAfterMonth"
          :key="'empty-after-' + _"
          class="max-sm:h-20 sm:h-24 border border-gray-100"
        ></div>
      </div>
    </div>
  </div>
</template>
