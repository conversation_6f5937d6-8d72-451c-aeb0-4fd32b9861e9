<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import FullCalendar from '@/components/common/shared/FullCalendar.vue';
import Button from '@/components/common/shared/Button.vue';
import { ref } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { useRoute } from 'ziggy-js';
import { format, parseISO, isValid } from 'date-fns';

const { t } = useI18n();
const route = useRoute();
const page = usePage();

const date =
  page.props.date && typeof page.props.date === 'string'
    ? isValid(parseISO(page.props.date))
      ? parseISO(page.props.date)
      : new Date()
    : new Date();

const currentViewMonth = ref(date);
const isVisible = ref(true);

function toggleVisible() {
  isVisible.value = !isVisible.value;
}

const handleEventClick = payload => {
  console.log('event click', payload);
};

const handleDayClick = (payload: { date: Date; events: object }) => {
  router.get(route('admin.job-application.index', { date: format(payload.date, 'yyyy-MM-dd') }));
};

const handleMonthChange = async (payload: { month: Date }) => {
  currentViewMonth.value = payload.month;
  router.get(route('admin.job-application.index', { date: format(payload.month, 'yyyy-MM-dd'), is_month: true }));
};
</script>

<template>
  <div class="rounded-2xl border border-gray-200 bg-white mb-6">
    <div class="flex items-center justify-between p-4">
      <h4 class="text-lg font-semibold text-gray-800">
        {{ t('Calendar') }}
      </h4>
      <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleVisible">
        <ChevronUpIcon v-if="isVisible" class="w-5 h-5" />
        <ChevronDownIcon v-else class="w-5 h-5" />
      </Button>
    </div>
    <div class="custom-calendar" v-show="isVisible">
      <FullCalendar
        ref="calendarRef"
        :events="[]"
        :initialMonth="currentViewMonth"
        :day-selected="currentViewMonth"
        @event-click="handleEventClick"
        @day-click="handleDayClick"
        @month-change="handleMonthChange"
      />
    </div>
  </div>
</template>

<style scoped></style>
