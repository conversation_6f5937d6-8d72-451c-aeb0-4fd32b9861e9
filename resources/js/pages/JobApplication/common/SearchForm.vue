<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import DatePicker from '@/components/common/shared/DatePicker.vue';
import Button from '@/components/common/shared/Button.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import { useI18n } from '@/composables/useI18n.ts';
import { computed, ref } from 'vue';
import { useForm } from '@inertiajs/vue3';

const { t } = useI18n();

defineEmits(['search', 'reset']);

const isSearchVisible = ref(false);

const prefectureOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  ...(window as any).prefectureOptions,
]);

const searchForm = useForm({
  title: '',
  prefecture: '',
  time_start: '',
  time_end: '',
  is_filled: '',
});

function toggleSearchForm() {
  isSearchVisible.value = !isSearchVisible.value;
}
</script>

<template>
  <div class="p-4 mb-6 border border-gray-200 rounded-2xl">
    <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
      <div class="w-full">
        <div class="flex items-center justify-between" :class="isSearchVisible ? 'mb-6' : ''">
          <h4 class="text-lg font-semibold text-gray-800">
            {{ t('common.field.search') }}
          </h4>
          <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleSearchForm">
            <ChevronUpIcon v-if="isSearchVisible" class="w-5 h-5" />
            <ChevronDownIcon v-else class="w-5 h-5" />
          </Button>
        </div>
        <form @submit.prevent="$emit('search')" v-show="isSearchVisible" class="transition-all duration-300">
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-7">
            <v-input class="col-span-2" :label="t('models/job.field.title')" v-model="searchForm.title" />
            <div class="col-span-1">
              <label class="mb-1.5 block text-sm font-medium text-gray-700">{{
                t('models/job.searchField.time')
              }}</label>
              <div class="grid grid-cols-2 gap-4 items-center">
                <DatePicker type="time" v-model="searchForm.time_start" />
                <DatePicker type="time" v-model="searchForm.time_end" />
              </div>
            </div>
            <v-select
              :label="t('models/job.field.prefecture')"
              v-model="searchForm.prefecture"
              :options="prefectureOptions"
            />
          </div>
          <div class="flex items-center justify-center gap-2 mt-6">
            <Button size="sm" variant="outline" @click="$emit('reset')">
              {{ t('common.btn.reset') }}
            </Button>
            <Button size="sm" variant="primary" type="submit">
              {{ t('common.btn.search') }}
            </Button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
